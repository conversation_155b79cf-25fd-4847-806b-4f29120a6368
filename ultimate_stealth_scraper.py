"""
Ultimate Stealth Google Search Screenshot Automation - 2025 Edition
Combines the latest anti-detection techniques based on research
"""

import asyncio
import os
import pandas as pd
import random
from datetime import datetime
from playwright.async_api import async_playwright
import re
import time

class UltimateStealthScraper:
    def __init__(self, screenshots_folder="ultimate_stealth_screenshots"):
        """Initialize with 2025's most advanced anti-detection techniques"""
        self.screenshots_folder = screenshots_folder
        os.makedirs(self.screenshots_folder, exist_ok=True)
        
        # Chrome profile path
        username = os.getenv('USERNAME')
        self.chrome_profile_path = f"C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"
        
        # 2025 Latest User Agents (Real browser versions)
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.0.0"
        ]
        
        # Realistic viewport sizes (common resolutions)
        self.viewports = [
            {"width": 1920, "height": 1080},
            {"width": 1366, "height": 768},
            {"width": 1536, "height": 864},
            {"width": 1440, "height": 900},
            {"width": 1280, "height": 720},
            {"width": 1600, "height": 900},
            {"width": 2560, "height": 1440},
            {"width": 1680, "height": 1050}
        ]
        
        # 2025 Advanced Stealth Arguments
        self.stealth_args = [
            # Core automation hiding
            "--disable-blink-features=AutomationControlled",
            "--exclude-switches=enable-automation",
            "--disable-automation",
            "--disable-extensions-except",
            "--disable-plugins-discovery",
            
            # Browser behavior normalization
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-default-apps",
            "--disable-popup-blocking",
            "--disable-translate",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI,VizDisplayCompositor",
            
            # Memory and performance
            "--disable-dev-shm-usage",
            "--disable-gpu-sandbox",
            "--disable-software-rasterizer",
            "--disable-background-networking",
            
            # Additional stealth
            "--disable-client-side-phishing-detection",
            "--disable-sync",
            "--disable-component-extensions-with-background-pages",
            "--disable-extensions",
            "--disable-plugins",
            "--disable-hang-monitor",
            "--disable-prompt-on-repost",
            "--disable-domain-reliability",
            "--disable-component-update",
            
            # Network behavior
            "--aggressive-cache-discard",
            "--disable-background-timer-throttling",
            "--disable-ipc-flooding-protection"
        ]
        
        self.bot_indicators = [
            'unusual traffic', 'captcha', 'not a robot', 'verify you are human',
            'suspicious activity', 'automated queries', 'recaptcha', 'prove you are human',
            'security check', 'verify that you are not a robot'
        ]
    
    def create_ultimate_excel(self):
        """Create Excel with very natural, diverse search queries"""
        search_data = [
            # Very human-like searches with natural language
            {'search_query': 'pizza delivery near me open now', 'category': 'Food'},
            {'search_query': 'dentist appointment today', 'category': 'Healthcare'},
            {'search_query': 'coffee shop with good wifi', 'category': 'Food'},
            {'search_query': 'car mechanic honest reviews', 'category': 'Automotive'},
            {'search_query': 'hair salon walk in appointments', 'category': 'Beauty'},
            {'search_query': 'emergency plumber 24 hour', 'category': 'Home Services'},
            {'search_query': 'gym membership no contract', 'category': 'Fitness'},
            {'search_query': 'vet clinic emergency hours', 'category': 'Pet Services'},
            {'search_query': 'lawyer free consultation personal injury', 'category': 'Legal'},
            {'search_query': 'houses for sale under 300k', 'category': 'Real Estate'}
        ]
        
        df = pd.DataFrame(search_data)
        df['screenshot_taken'] = False
        df['screenshot_filename'] = ''
        df['timestamp'] = ''
        df['status'] = 'Pending'
        df['notes'] = ''
        df['bot_detected'] = False
        df['user_agent_used'] = ''
        df['viewport_used'] = ''
        df['delay_used'] = ''
        
        filename = f"ultimate_stealth_searches_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df.to_excel(filename, sheet_name='Search Queries', index=False)
        
        print(f"✅ Created ultimate Excel file: {filename}")
        print(f"📊 Total searches: {len(df)}")
        
        return filename, df
    
    def _sanitize_filename(self, text):
        """Create safe filename"""
        safe_text = re.sub(r'[^\w\s-]', '', text)
        safe_text = re.sub(r'[-\s]+', '_', safe_text)
        return safe_text[:50]
    
    async def _setup_ultimate_stealth(self, page):
        """Setup 2025's most advanced stealth configurations"""
        
        # Advanced JavaScript injection to hide automation
        await page.add_init_script("""
            // Remove webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // Override the plugins property to use a custom getter
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // Override the languages property to use a custom getter
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
            
            // Override the permissions property
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            
            // Override chrome runtime
            if (window.chrome) {
                Object.defineProperty(window.chrome, 'runtime', {
                    get: () => ({
                        onConnect: undefined,
                        onMessage: undefined,
                    }),
                });
            }
            
            // Override notification permission
            Object.defineProperty(Notification, 'permission', {
                get: () => 'default',
            });
            
            // Hide automation indicators
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            
            // Override toString methods
            window.navigator.webdriver = undefined;
            
            // Mock realistic screen properties
            Object.defineProperty(window.screen, 'availHeight', {
                get: () => window.screen.height - 40
            });
            
            // Add realistic timing
            const originalSetTimeout = window.setTimeout;
            window.setTimeout = function(callback, delay) {
                return originalSetTimeout(callback, delay + Math.random() * 10);
            };
        """)
        
        # Set random but realistic viewport
        viewport = random.choice(self.viewports)
        await page.set_viewport_size(viewport["width"], viewport["height"])
        
        # Set random user agent with realistic headers
        user_agent = random.choice(self.user_agents)
        await page.set_extra_http_headers({
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"'
        })
        
        return user_agent, viewport
    
    async def search_and_screenshot(self, search_query, search_index):
        """Ultimate stealth search with maximum human-like behavior"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_query = self._sanitize_filename(search_query)
        screenshot_filename = f"{timestamp}_{safe_query}.png"
        screenshot_path = os.path.join(self.screenshots_folder, screenshot_filename)
        
        # Variable delay before starting (human-like)
        pre_delay = random.uniform(2, 8)
        print(f"⏳ Pre-search delay: {pre_delay:.1f} seconds")
        await asyncio.sleep(pre_delay)
        
        async with async_playwright() as p:
            browser = None
            try:
                print(f"🚀 Launching ultimate stealth browser for: {search_query}")
                
                # Launch with maximum stealth
                browser = await p.chromium.launch_persistent_context(
                    user_data_dir=self.chrome_profile_path,
                    headless=False,
                    args=self.stealth_args,
                    ignore_default_args=["--enable-automation", "--enable-blink-features=AutomationControlled"],
                    slow_mo=random.randint(100, 300),  # Random slow motion
                    timeout=60000
                )
                
                # Get page
                if len(browser.pages) > 0:
                    page = browser.pages[0]
                else:
                    page = await browser.new_page()
                
                # Setup ultimate stealth
                user_agent, viewport = await self._setup_ultimate_stealth(page)
                
                print(f"🕵️ UA: {user_agent[:50]}...")
                print(f"📺 Viewport: {viewport['width']}x{viewport['height']}")
                
                # Very human-like delay before navigation
                nav_delay = random.uniform(3, 6)
                await asyncio.sleep(nav_delay)
                
                # Navigate to Google with realistic timing
                print("🌐 Navigating to Google...")
                await page.goto('https://www.google.com', wait_until='networkidle', timeout=30000)
                
                # Random delay and check for bot detection
                check_delay = random.uniform(4, 7)
                await asyncio.sleep(check_delay)
                page_content = await page.content()
                
                if any(indicator in page_content.lower() for indicator in self.bot_indicators):
                    print("🚨 Bot detection found on homepage!")
                    await page.screenshot(path=screenshot_path)
                    return False, screenshot_filename, "Bot detection on homepage", True, user_agent, viewport, pre_delay
                
                print("✅ No bot detection on homepage")
                
                # Find search box with multiple attempts
                search_box = None
                search_selectors = ['textarea[name="q"]', 'input[name="q"]', '#APjFqb', '[title="Search"]']
                
                for selector in search_selectors:
                    try:
                        search_box = await page.wait_for_selector(selector, timeout=10000)
                        print(f"✅ Found search box with: {selector}")
                        break
                    except:
                        continue
                
                if not search_box:
                    await page.screenshot(path=screenshot_path)
                    return False, screenshot_filename, "Could not find search box", False, user_agent, viewport, pre_delay
                
                # Ultra-human-like search behavior
                print(f"⌨️ Typing search query with ultra-human behavior...")
                
                # Click and focus with realistic delay
                await search_box.click()
                focus_delay = random.uniform(1.2, 2.5)
                await asyncio.sleep(focus_delay)
                
                # Type with very realistic human timing patterns
                for i, char in enumerate(search_query):
                    await search_box.type(char)
                    
                    # Realistic typing patterns
                    if char == ' ':
                        # Longer pause at spaces (thinking)
                        await asyncio.sleep(random.uniform(0.15, 0.4))
                    elif char in 'aeiou':
                        # Vowels typed slightly faster
                        await asyncio.sleep(random.uniform(0.06, 0.15))
                    else:
                        # Consonants with normal speed
                        await asyncio.sleep(random.uniform(0.08, 0.25))
                    
                    # Occasional longer pauses (like thinking or correcting)
                    if i > 0 and i % random.randint(4, 8) == 0:
                        thinking_pause = random.uniform(0.5, 1.5)
                        await asyncio.sleep(thinking_pause)
                
                # Pause before pressing Enter (reading what was typed)
                review_delay = random.uniform(1.5, 3.0)
                await asyncio.sleep(review_delay)
                await search_box.press('Enter')
                
                # Wait for results with extended timeout
                print("⏳ Waiting for search results...")
                try:
                    await page.wait_for_selector('#search', timeout=25000)
                except:
                    try:
                        await page.wait_for_load_state('networkidle', timeout=25000)
                    except:
                        pass
                
                # Additional wait for dynamic content and ads
                results_delay = random.uniform(5, 8)
                await asyncio.sleep(results_delay)
                
                # Check for bot detection in results
                search_content = await page.content()
                bot_detected = any(indicator in search_content.lower() for indicator in self.bot_indicators)
                
                # Take screenshot
                await page.screenshot(path=screenshot_path, full_page=True)
                
                if bot_detected:
                    print("🚨 Bot detection found in search results!")
                    return False, screenshot_filename, "Bot detection in search results", True, user_agent, viewport, pre_delay
                else:
                    print("✅ Search successful - no bot detection!")
                    return True, screenshot_filename, "Success", False, user_agent, viewport, pre_delay
                
            except Exception as e:
                error_msg = str(e)
                print(f"❌ Error for '{search_query}': {error_msg}")
                return False, "", error_msg, False, "", "", 0
            finally:
                if browser:
                    await browser.close()
                    # Extended wait between searches
                    post_delay = random.uniform(8, 15)
                    print(f"⏳ Post-search delay: {post_delay:.1f} seconds")
                    await asyncio.sleep(post_delay)
    
    async def process_searches(self, excel_file, df, base_delay=30):
        """Process searches with ultimate stealth techniques"""
        total_searches = len(df)
        successful = 0
        failed = 0
        bot_detected_count = 0
        
        print(f"\n🎯 Starting ultimate stealth processing of {total_searches} searches...")
        print(f"⏱️  Base delay between searches: {base_delay} seconds")
        print("-" * 60)
        
        for index, row in df.iterrows():
            search_query = row['search_query']
            print(f"\n[{index + 1}/{total_searches}] Processing: {search_query}")
            
            # Perform search
            success, filename, error, bot_detected, user_agent, viewport, delay_used = await self.search_and_screenshot(search_query, index)
            
            # Update DataFrame
            df.at[index, 'screenshot_taken'] = success
            df.at[index, 'screenshot_filename'] = filename
            df.at[index, 'timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            df.at[index, 'bot_detected'] = bot_detected
            df.at[index, 'user_agent_used'] = str(user_agent)[:50] if user_agent else ""
            df.at[index, 'viewport_used'] = f"{viewport['width']}x{viewport['height']}" if viewport else ""
            df.at[index, 'delay_used'] = f"{delay_used:.1f}s" if delay_used else ""
            
            if success:
                df.at[index, 'status'] = 'Completed'
                df.at[index, 'notes'] = 'Screenshot taken successfully'
                successful += 1
            else:
                if bot_detected:
                    df.at[index, 'status'] = 'Bot Detected'
                    bot_detected_count += 1
                else:
                    df.at[index, 'status'] = 'Failed'
                df.at[index, 'notes'] = error
                failed += 1
            
            # Save progress
            df.to_excel(excel_file, sheet_name='Search Queries', index=False)
            
            # Adaptive delay between searches
            if index < total_searches - 1:
                # Increase delay significantly if bot detection is high
                if bot_detected_count > (index + 1) * 0.2:  # More than 20% bot detection
                    actual_delay = random.uniform(base_delay * 2, base_delay * 3)
                    print(f"🚨 High bot detection - extending delay to {actual_delay:.1f} seconds...")
                elif bot_detected_count > (index + 1) * 0.1:  # More than 10% bot detection
                    actual_delay = random.uniform(base_delay * 1.5, base_delay * 2)
                    print(f"⚠️ Some bot detection - moderate delay: {actual_delay:.1f} seconds...")
                else:
                    actual_delay = random.uniform(base_delay * 0.8, base_delay * 1.2)
                    print(f"⏳ Normal delay: {actual_delay:.1f} seconds...")
                
                await asyncio.sleep(actual_delay)
        
        # Calculate rates
        success_rate = (successful / total_searches) * 100
        bot_detection_rate = (bot_detected_count / total_searches) * 100
        
        # Final summary
        print("\n" + "=" * 60)
        print("🎉 ULTIMATE STEALTH PROCESSING COMPLETE!")
        print(f"✅ Successful: {successful}")
        print(f"🚨 Bot Detected: {bot_detected_count}")
        print(f"❌ Other Failures: {failed}")
        print(f"📊 Total: {total_searches}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        print(f"🚨 Bot Detection Rate: {bot_detection_rate:.1f}%")
        print("=" * 60)
        
        return success_rate, bot_detection_rate

async def main():
    """Main function"""
    print("🕵️ Ultimate Stealth Google Search Screenshot Automation - 2025 Edition")
    print("=" * 70)
    
    scraper = UltimateStealthScraper()
    
    # Create ultimate test data
    excel_file, df = scraper.create_ultimate_excel()
    
    # Process with extended delays for maximum stealth
    success_rate, bot_rate = await scraper.process_searches(excel_file, df, base_delay=35)
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    print(f"🚨 Bot Detection Rate: {bot_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 EXCELLENT! Target achieved!")
    elif success_rate >= 80:
        print("👍 VERY GOOD! Close to target")
    elif success_rate >= 70:
        print("⚠️ GOOD - Getting better")
    elif success_rate >= 60:
        print("⚠️ MODERATE - Needs improvement")
    else:
        print("❌ NEEDS SIGNIFICANT IMPROVEMENT")

if __name__ == "__main__":
    asyncio.run(main())
