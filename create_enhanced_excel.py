"""
Create enhanced Excel file with diverse dummy search queries
Designed to test anti-detection capabilities
"""

import pandas as pd
from datetime import datetime
import random

def create_enhanced_search_excel():
    """Create Excel file with enhanced dummy search queries"""
    
    # More diverse and realistic search data
    search_data = [
        # Local business searches (most common)
        {
            'search_query': 'best pizza near me',
            'category': 'Food & Dining',
            'location': 'Local',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'dentist office hours',
            'category': 'Healthcare',
            'location': 'Local',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'coffee shop wifi',
            'category': 'Food & Dining',
            'location': 'Local',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'auto repair shop reviews',
            'category': 'Automotive',
            'location': 'Local',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'hair salon appointment booking',
            'category': 'Beauty & Personal Care',
            'location': 'Local',
            'search_type': 'Local Business'
        },
        
        # Informational searches
        {
            'search_query': 'how to change car oil',
            'category': 'Automotive',
            'location': 'General',
            'search_type': 'Informational'
        },
        {
            'search_query': 'best restaurants downtown',
            'category': 'Food & Dining',
            'location': 'General',
            'search_type': 'Informational'
        },
        {
            'search_query': 'weather forecast this week',
            'category': 'Weather',
            'location': 'General',
            'search_type': 'Informational'
        },
        
        # Shopping searches
        {
            'search_query': 'laptop deals under 500',
            'category': 'Electronics',
            'location': 'General',
            'search_type': 'Shopping'
        },
        {
            'search_query': 'running shoes for beginners',
            'category': 'Sports & Fitness',
            'location': 'General',
            'search_type': 'Shopping'
        },
        
        # Service searches
        {
            'search_query': 'plumber emergency service',
            'category': 'Home Services',
            'location': 'Local',
            'search_type': 'Service'
        },
        {
            'search_query': 'tax preparation services',
            'category': 'Professional Services',
            'location': 'Local',
            'search_type': 'Service'
        },
        
        # Entertainment searches
        {
            'search_query': 'movie theaters showtimes',
            'category': 'Entertainment',
            'location': 'Local',
            'search_type': 'Entertainment'
        },
        {
            'search_query': 'live music venues tonight',
            'category': 'Entertainment',
            'location': 'Local',
            'search_type': 'Entertainment'
        },
        
        # Health searches
        {
            'search_query': 'urgent care walk in clinic',
            'category': 'Healthcare',
            'location': 'Local',
            'search_type': 'Healthcare'
        }
    ]
    
    # Create DataFrame
    df = pd.DataFrame(search_data)
    
    # Add tracking columns
    df['screenshot_taken'] = False
    df['screenshot_filename'] = ''
    df['timestamp'] = ''
    df['status'] = 'Pending'
    df['notes'] = ''
    df['attempt_number'] = 1
    df['user_agent_used'] = ''
    df['viewport_used'] = ''
    
    # Save to Excel with timestamp
    filename = f"enhanced_search_queries_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    # Create Excel writer with formatting
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Search Queries', index=False)
        
        # Get the workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['Search Queries']
        
        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    print(f"✅ Enhanced Excel file created: {filename}")
    print(f"📊 Total searches: {len(df)}")
    print("\nSearch categories:")
    for category in df['category'].value_counts().items():
        print(f"  - {category[0]}: {category[1]} searches")
    
    print("\nSearch types:")
    for search_type in df['search_type'].value_counts().items():
        print(f"  - {search_type[0]}: {search_type[1]} searches")
    
    return filename, df

if __name__ == "__main__":
    create_enhanced_search_excel()
