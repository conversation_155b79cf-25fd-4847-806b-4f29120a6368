"""
Advanced Stealth Google Search Screenshot Automation
Implements multiple advanced anti-detection techniques
"""

import asyncio
import os
import pandas as pd
import random
from datetime import datetime
from playwright.async_api import async_playwright
import re

class AdvancedStealthScraper:
    def __init__(self, screenshots_folder="advanced_stealth_screenshots"):
        """Initialize with advanced anti-detection techniques"""
        self.screenshots_folder = screenshots_folder
        os.makedirs(self.screenshots_folder, exist_ok=True)
        
        # Multiple Chrome profiles for rotation
        username = os.getenv('USERNAME')
        self.chrome_profiles = [
            f"C:/Users/<USER>/AppData/Local/Google/Chrome/User Data",
            f"C:/Users/<USER>/AppData/Local/Google/Chrome Beta/User Data",
            f"C:/Users/<USER>/AppData/Local/Microsoft/Edge/User Data"
        ]
        
        # Filter existing profiles
        self.chrome_profiles = [p for p in self.chrome_profiles if os.path.exists(p)]
        if not self.chrome_profiles:
            self.chrome_profiles = [f"C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"]
        
        print(f"📁 Using {len(self.chrome_profiles)} browser profile(s)")
        
        # Diverse user agents (latest versions)
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
        ]
        
        # Viewport variations
        self.viewports = [
            {"width": 1920, "height": 1080},
            {"width": 1366, "height": 768},
            {"width": 1536, "height": 864},
            {"width": 1440, "height": 900},
            {"width": 1280, "height": 720},
            {"width": 1600, "height": 900}
        ]
        
        # Advanced stealth arguments
        self.stealth_args = [
            # Core stealth
            "--disable-blink-features=AutomationControlled",
            "--exclude-switches=enable-automation",
            "--disable-automation",
            "--disable-extensions-except",
            "--disable-plugins-discovery",
            
            # Browser behavior
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-default-apps",
            "--disable-popup-blocking",
            "--disable-translate",
            
            # Performance and detection avoidance
            "--disable-features=VizDisplayCompositor,TranslateUI",
            "--disable-ipc-flooding-protection",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-background-timer-throttling",
            "--disable-component-extensions-with-background-pages",
            
            # Memory and process
            "--disable-dev-shm-usage",
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-gpu-sandbox",
            
            # Additional stealth
            "--disable-web-security",
            "--allow-running-insecure-content",
            "--disable-client-side-phishing-detection",
            "--disable-sync",
            "--metrics-recording-only",
            "--no-report-upload"
        ]
        
        self.bot_indicators = [
            'unusual traffic', 'captcha', 'not a robot', 'verify you are human',
            'suspicious activity', 'automated queries', 'recaptcha', 'prove you are human'
        ]
    
    def create_diverse_excel(self):
        """Create Excel with very diverse, natural search queries"""
        search_data = [
            # Very natural, human-like searches
            {'search_query': 'pizza delivery near me', 'category': 'Food'},
            {'search_query': 'dentist open today', 'category': 'Healthcare'},
            {'search_query': 'coffee shop with wifi', 'category': 'Food'},
            {'search_query': 'car mechanic reviews', 'category': 'Automotive'},
            {'search_query': 'hair cut appointment', 'category': 'Beauty'},
            {'search_query': 'emergency plumber', 'category': 'Home Services'},
            {'search_query': 'gym near me hours', 'category': 'Fitness'},
            {'search_query': 'vet clinic walk in', 'category': 'Pet Services'},
            {'search_query': 'lawyer free consultation', 'category': 'Legal'},
            {'search_query': 'houses for sale', 'category': 'Real Estate'}
        ]
        
        df = pd.DataFrame(search_data)
        df['screenshot_taken'] = False
        df['screenshot_filename'] = ''
        df['timestamp'] = ''
        df['status'] = 'Pending'
        df['notes'] = ''
        df['bot_detected'] = False
        df['profile_used'] = ''
        df['user_agent_used'] = ''
        
        filename = f"advanced_stealth_searches_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df.to_excel(filename, sheet_name='Search Queries', index=False)
        
        print(f"✅ Created diverse Excel file: {filename}")
        print(f"📊 Total searches: {len(df)}")
        
        return filename, df
    
    def _sanitize_filename(self, text):
        """Create safe filename"""
        safe_text = re.sub(r'[^\w\s-]', '', text)
        safe_text = re.sub(r'[-\s]+', '_', safe_text)
        return safe_text[:50]
    
    async def _setup_advanced_stealth(self, page):
        """Setup advanced stealth configurations"""
        
        # Remove webdriver traces
        await page.add_init_script("""
            // Remove webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // Override permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            
            // Override plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // Override languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
            
            // Override chrome runtime
            if (window.chrome) {
                Object.defineProperty(window.chrome, 'runtime', {
                    get: () => ({
                        onConnect: undefined,
                        onMessage: undefined,
                    }),
                });
            }
            
            // Override notification permission
            Object.defineProperty(Notification, 'permission', {
                get: () => 'default',
            });
        """)
        
        # Set random viewport
        viewport = random.choice(self.viewports)
        await page.set_viewport_size(viewport["width"], viewport["height"])
        
        # Set random user agent and headers
        user_agent = random.choice(self.user_agents)
        await page.set_extra_http_headers({
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        })
        
        return user_agent, viewport
    
    async def search_and_screenshot(self, search_query, search_index):
        """Advanced stealth search with profile rotation"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_query = self._sanitize_filename(search_query)
        screenshot_filename = f"{timestamp}_{safe_query}.png"
        screenshot_path = os.path.join(self.screenshots_folder, screenshot_filename)
        
        # Rotate Chrome profiles
        profile_path = self.chrome_profiles[search_index % len(self.chrome_profiles)]
        
        async with async_playwright() as p:
            browser = None
            try:
                print(f"🚀 Launching advanced stealth browser for: {search_query}")
                print(f"📁 Using profile: {os.path.basename(profile_path)}")
                
                # Launch with advanced stealth
                browser = await p.chromium.launch_persistent_context(
                    user_data_dir=profile_path,
                    headless=False,
                    args=self.stealth_args,
                    ignore_default_args=["--enable-automation", "--enable-blink-features=AutomationControlled"],
                    slow_mo=random.randint(50, 150)  # Random slow motion
                )
                
                # Get page
                if len(browser.pages) > 0:
                    page = browser.pages[0]
                else:
                    page = await browser.new_page()
                
                # Setup advanced stealth
                user_agent, viewport = await self._setup_advanced_stealth(page)
                
                print(f"🕵️ UA: {user_agent[:50]}...")
                print(f"📺 Viewport: {viewport['width']}x{viewport['height']}")
                
                # Human-like delay before navigation
                await asyncio.sleep(random.uniform(2, 4))
                
                # Navigate to Google
                print("🌐 Navigating to Google...")
                await page.goto('https://www.google.com', wait_until='networkidle')
                
                # Random delay and check for bot detection
                await asyncio.sleep(random.uniform(3, 5))
                page_content = await page.content()
                
                if any(indicator in page_content.lower() for indicator in self.bot_indicators):
                    print("🚨 Bot detection found on homepage!")
                    await page.screenshot(path=screenshot_path)
                    return False, screenshot_filename, "Bot detection on homepage", True, profile_path, user_agent
                
                print("✅ No bot detection on homepage")
                
                # Find search box with patience
                search_box = None
                search_selectors = ['textarea[name="q"]', 'input[name="q"]', '#APjFqb']
                
                for selector in search_selectors:
                    try:
                        search_box = await page.wait_for_selector(selector, timeout=8000)
                        print(f"✅ Found search box with: {selector}")
                        break
                    except:
                        continue
                
                if not search_box:
                    await page.screenshot(path=screenshot_path)
                    return False, screenshot_filename, "Could not find search box", False, profile_path, user_agent
                
                # Very human-like search behavior
                print(f"⌨️ Typing search query with human behavior...")
                
                # Click and focus
                await search_box.click()
                await asyncio.sleep(random.uniform(0.8, 1.5))
                
                # Type with very human-like timing
                for i, char in enumerate(search_query):
                    await search_box.type(char)
                    # Vary typing speed - faster for common words, slower for complex ones
                    if char == ' ':
                        await asyncio.sleep(random.uniform(0.1, 0.3))
                    else:
                        await asyncio.sleep(random.uniform(0.08, 0.2))
                    
                    # Occasional pause (like thinking)
                    if i > 0 and i % random.randint(3, 7) == 0:
                        await asyncio.sleep(random.uniform(0.3, 0.8))
                
                # Pause before pressing Enter
                await asyncio.sleep(random.uniform(1, 2))
                await search_box.press('Enter')
                
                # Wait for results with longer timeout
                print("⏳ Waiting for search results...")
                try:
                    await page.wait_for_selector('#search', timeout=20000)
                except:
                    await page.wait_for_load_state('networkidle', timeout=20000)
                
                # Additional wait for dynamic content
                await asyncio.sleep(random.uniform(4, 6))
                
                # Check for bot detection in results
                search_content = await page.content()
                bot_detected = any(indicator in search_content.lower() for indicator in self.bot_indicators)
                
                # Take screenshot
                await page.screenshot(path=screenshot_path, full_page=True)
                
                if bot_detected:
                    print("🚨 Bot detection found in search results!")
                    return False, screenshot_filename, "Bot detection in search results", True, profile_path, user_agent
                else:
                    print("✅ Search successful - no bot detection!")
                    return True, screenshot_filename, "Success", False, profile_path, user_agent
                
            except Exception as e:
                error_msg = str(e)
                print(f"❌ Error for '{search_query}': {error_msg}")
                return False, "", error_msg, False, "", ""
            finally:
                if browser:
                    await browser.close()
                    # Longer wait between searches
                    await asyncio.sleep(random.uniform(5, 8))
    
    async def process_searches(self, excel_file, df, delay_between_searches=20):
        """Process searches with advanced techniques"""
        total_searches = len(df)
        successful = 0
        failed = 0
        bot_detected_count = 0
        
        print(f"\n🎯 Starting advanced stealth processing of {total_searches} searches...")
        print(f"⏱️  Base delay between searches: {delay_between_searches} seconds")
        print("-" * 60)
        
        for index, row in df.iterrows():
            search_query = row['search_query']
            print(f"\n[{index + 1}/{total_searches}] Processing: {search_query}")
            
            # Perform search
            success, filename, error, bot_detected, profile, user_agent = await self.search_and_screenshot(search_query, index)
            
            # Update DataFrame
            df.at[index, 'screenshot_taken'] = success
            df.at[index, 'screenshot_filename'] = filename
            df.at[index, 'timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            df.at[index, 'bot_detected'] = bot_detected
            df.at[index, 'profile_used'] = os.path.basename(profile) if profile else ""
            df.at[index, 'user_agent_used'] = user_agent[:50] if user_agent else ""
            
            if success:
                df.at[index, 'status'] = 'Completed'
                df.at[index, 'notes'] = 'Screenshot taken successfully'
                successful += 1
            else:
                if bot_detected:
                    df.at[index, 'status'] = 'Bot Detected'
                    bot_detected_count += 1
                else:
                    df.at[index, 'status'] = 'Failed'
                df.at[index, 'notes'] = error
                failed += 1
            
            # Save progress
            df.to_excel(excel_file, sheet_name='Search Queries', index=False)
            
            # Variable delay between searches
            if index < total_searches - 1:
                # Increase delay if bot detection is high
                if bot_detected_count > (index + 1) * 0.3:  # More than 30% bot detection
                    actual_delay = random.uniform(delay_between_searches * 1.5, delay_between_searches * 2)
                    print(f"🚨 High bot detection - extending delay to {actual_delay:.1f} seconds...")
                else:
                    actual_delay = random.uniform(delay_between_searches * 0.8, delay_between_searches * 1.2)
                    print(f"⏳ Waiting {actual_delay:.1f} seconds before next search...")
                
                await asyncio.sleep(actual_delay)
        
        # Calculate rates
        success_rate = (successful / total_searches) * 100
        bot_detection_rate = (bot_detected_count / total_searches) * 100
        
        # Final summary
        print("\n" + "=" * 60)
        print("🎉 ADVANCED STEALTH PROCESSING COMPLETE!")
        print(f"✅ Successful: {successful}")
        print(f"🚨 Bot Detected: {bot_detected_count}")
        print(f"❌ Other Failures: {failed}")
        print(f"📊 Total: {total_searches}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        print(f"🚨 Bot Detection Rate: {bot_detection_rate:.1f}%")
        print("=" * 60)
        
        return success_rate, bot_detection_rate

async def main():
    """Main function"""
    print("🕵️ Advanced Stealth Google Search Screenshot Automation")
    print("=" * 60)
    
    scraper = AdvancedStealthScraper()
    
    # Create diverse test data
    excel_file, df = scraper.create_diverse_excel()
    
    # Process with longer delays
    success_rate, bot_rate = await scraper.process_searches(excel_file, df, delay_between_searches=25)
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    print(f"🚨 Bot Detection Rate: {bot_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 EXCELLENT! Target achieved!")
    elif success_rate >= 80:
        print("👍 GOOD! Close to target")
    elif success_rate >= 70:
        print("⚠️ MODERATE - Getting better")
    else:
        print("❌ NEEDS MORE IMPROVEMENT")

if __name__ == "__main__":
    asyncio.run(main())
