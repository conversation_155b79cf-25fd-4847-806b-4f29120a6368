"""
Quick test to demonstrate using your Chrome profile for Google search screenshots
"""

import asyncio
import os
from playwright.async_api import async_playwright

async def test_chrome_profile_search():
    """Test searching Google with your Chrome profile"""
    
    # Your Chrome profile path (Windows)
    username = os.getenv('USERNAME')
    chrome_profile_path = f"C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"
    
    print(f"Using Chrome profile: {chrome_profile_path}")
    print("Make sure Chrome is completely closed before running this!")
    
    async with async_playwright() as p:
        try:
            # Launch Chrome with your profile
            print("Launching Chrome with your profile...")
            browser = await p.chromium.launch_persistent_context(
                user_data_dir=chrome_profile_path,
                headless=False,  # Set to True for headless mode
                args=[
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-blink-features=AutomationControlled',
                ]
            )
            
            # Get or create page
            if len(browser.pages) > 0:
                page = browser.pages[0]
            else:
                page = await browser.new_page()
            
            # Navigate to Google
            print("Going to Google...")
            await page.goto('https://www.google.com', wait_until='networkidle')

            # Wait a moment for page to fully load
            await asyncio.sleep(2)

            # Take a screenshot of the initial page to see what we got
            await page.screenshot(path="google_homepage.png")
            print("Took screenshot of homepage: google_homepage.png")

            # Search for something
            search_query = "best pizza in New York"
            print(f"Searching for: {search_query}")

            # Try multiple selectors for the search box
            search_box = None
            selectors = [
                'input[name="q"]',
                'textarea[name="q"]',
                '[data-ved] input',
                'input[type="text"]',
                '#APjFqb'
            ]

            for selector in selectors:
                try:
                    print(f"Trying selector: {selector}")
                    search_box = await page.wait_for_selector(selector, timeout=3000)
                    print(f"Found search box with selector: {selector}")
                    break
                except:
                    continue

            if not search_box:
                print("Could not find search box, taking screenshot for debugging...")
                await page.screenshot(path="debug_no_search_box.png")
                raise Exception("Could not find search box")

            await search_box.fill(search_query)
            await search_box.press('Enter')
            
            # Wait for results
            print("Waiting for search results...")
            await page.wait_for_load_state('networkidle')
            await asyncio.sleep(3)  # Extra wait for dynamic content
            
            # Take screenshot
            screenshot_name = "google_search_with_profile.png"
            print(f"Taking screenshot: {screenshot_name}")
            await page.screenshot(path=screenshot_name, full_page=True)
            
            print(f"✅ Success! Screenshot saved as: {screenshot_name}")
            print("Check the current directory for the screenshot file.")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            print("\nTroubleshooting tips:")
            print("1. Make sure Chrome is completely closed")
            print("2. Check if the Chrome profile path exists")
            print("3. Try running as administrator")
            
        finally:
            if 'browser' in locals():
                await browser.close()

if __name__ == "__main__":
    print("=== Google Search Screenshot with Chrome Profile ===")
    print()
    asyncio.run(test_chrome_profile_search())
