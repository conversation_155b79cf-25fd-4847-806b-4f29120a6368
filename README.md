# Google Search Screenshot Tool

This tool allows you to take screenshots of Google search results using your existing Chrome profile, which helps avoid bot detection.

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Install Playwright Browsers (if using <PERSON><PERSON>)

```bash
playwright install chromium
```

### 3. Find Your Chrome Profile Path

**Windows:**
```
C:\Users\<USER>\AppData\Local\Google\Chrome\User Data
```

**Mac:**
```
~/Library/Application Support/Google/Chrome
```

**Linux:**
```
~/.config/google-chrome
```

## Usage

### Option 1: Playwright (Recommended)

```python
from chrome_profile_screenshot import GoogleScreenshotter
import asyncio

async def main():
    screenshotter = GoogleScreenshotter()
    await screenshotter.search_and_screenshot("pizza restaurants in New York")

asyncio.run(main())
```

### Option 2: Selenium

```python
from selenium_chrome_profile import SeleniumGoogleScreenshotter

screenshotter = SeleniumGoogleScreenshotter()
screenshotter.search_and_screenshot("pizza restaurants in New York")
```

## Key Features

- ✅ Uses your existing Chrome profile (cookies, login sessions, history)
- ✅ Reduces bot detection significantly
- ✅ Supports both Playwright and Selenium
- ✅ Automatic screenshot naming with timestamps
- ✅ Full page screenshots
- ✅ Error handling and retries

## Important Notes

### Chrome Profile Considerations

1. **Close Chrome First**: Make sure Chrome is completely closed before running the script
2. **Profile Directory**: The script uses "Default" profile. If you use multiple profiles, you might need to change this to "Profile 1", "Profile 2", etc.
3. **Permissions**: Make sure the script has read access to your Chrome profile directory

### Finding Your Profile Directory

If you're not sure which profile to use:

1. Open Chrome
2. Go to `chrome://version/`
3. Look for "Profile Path" - this shows your current profile location

### Multiple Profiles

If you have multiple Chrome profiles, you can specify which one:

```python
# For Playwright
screenshotter = GoogleScreenshotter("C:/Users/<USER>/AppData/Local/Google/Chrome/User Data")

# For Selenium - modify the profile directory in setup_driver():
chrome_options.add_argument("--profile-directory=Profile 1")  # Change as needed
```

## Troubleshooting

### Common Issues

1. **"Chrome is already running"**: Close all Chrome windows and try again
2. **Profile not found**: Check the path and make sure Chrome profile exists
3. **Permission denied**: Run as administrator (Windows) or check file permissions
4. **ChromeDriver issues**: Make sure ChromeDriver is installed and in PATH

### Anti-Detection Tips

- The scripts include several anti-detection measures
- Using your real profile is the best anti-detection method
- Add random delays between actions if needed
- Don't run too many searches too quickly

## Example Output

Screenshots will be saved with names like:
- `google_search_20231225_143022.png`
- `google_pizza_restaurants_in_New_York_20231225_143045.png`
