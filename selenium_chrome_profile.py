"""
Google Search Screenshot Tool using Selenium with your Chrome profile
"""

import os
import time
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

class SeleniumGoogleScreenshotter:
    def __init__(self, chrome_profile_path=None):
        """
        Initialize with Chrome profile path
        """
        self.chrome_profile_path = chrome_profile_path or self._get_default_chrome_profile()
        self.driver = None
        
    def _get_default_chrome_profile(self):
        """Get default Chrome profile path for current OS"""
        import platform
        system = platform.system()
        
        if system == "Windows":
            username = os.getenv('USERNAME')
            return f"C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data"
        elif system == "Darwin":  # Mac
            return os.path.expanduser("~/Library/Application Support/Google/Chrome")
        else:  # Linux
            return os.path.expanduser("~/.config/google-chrome")
    
    def setup_driver(self, headless=False):
        """Setup Chrome driver with your profile"""
        chrome_options = Options()
        
        # Use your existing Chrome profile
        chrome_options.add_argument(f"--user-data-dir={self.chrome_profile_path}")
        chrome_options.add_argument("--profile-directory=Default")  # or "Profile 1", "Profile 2", etc.
        
        # Anti-detection options
        chrome_options.add_argument("--no-first-run")
        chrome_options.add_argument("--no-default-browser-check")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        if headless:
            chrome_options.add_argument("--headless")
        
        # Create driver
        self.driver = webdriver.Chrome(options=chrome_options)
        
        # Execute script to remove webdriver property
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return self.driver
    
    def search_and_screenshot(self, search_query, output_filename=None, headless=False):
        """
        Search Google and take a screenshot
        """
        if not output_filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_query = "".join(c for c in search_query if c.isalnum() or c in (' ', '-', '_')).rstrip()
            output_filename = f"google_{safe_query.replace(' ', '_')}_{timestamp}.png"
        
        try:
            # Setup driver
            print("Setting up Chrome driver with your profile...")
            self.setup_driver(headless=headless)
            
            # Navigate to Google
            print("Navigating to Google...")
            self.driver.get('https://www.google.com')
            
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.NAME, "q"))
            )
            
            # Find search box and search
            print(f"Searching for: {search_query}")
            search_box = self.driver.find_element(By.NAME, "q")
            search_box.clear()
            search_box.send_keys(search_query)
            search_box.send_keys(Keys.RETURN)
            
            # Wait for results
            print("Waiting for search results...")
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.ID, "search"))
            )
            
            # Additional wait for dynamic content
            time.sleep(3)
            
            # Take screenshot
            print(f"Taking screenshot: {output_filename}")
            self.driver.save_screenshot(output_filename)
            
            print(f"Screenshot saved successfully: {output_filename}")
            return output_filename
            
        except Exception as e:
            print(f"Error during search: {e}")
            return None
        finally:
            if self.driver:
                self.driver.quit()

def main():
    """Example usage"""
    screenshotter = SeleniumGoogleScreenshotter()
    
    # Example searches
    searches = [
        "pizza restaurants in New York",
        "coffee shops in San Francisco", 
        "hotels in Paris"
    ]
    
    for search in searches:
        print(f"\n--- Searching: {search} ---")
        result = screenshotter.search_and_screenshot(search, headless=False)
        if result:
            print(f"✅ Success: {result}")
        else:
            print("❌ Failed")
        
        # Wait between searches
        time.sleep(3)

if __name__ == "__main__":
    main()
