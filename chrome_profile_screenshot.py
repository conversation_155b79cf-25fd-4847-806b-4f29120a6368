"""
Google Search Screenshot Tool using your existing Chrome profile
This script uses <PERSON><PERSON> to launch Chrome with your personal profile
"""

import asyncio
import os
from playwright.async_api import async_playwright
from datetime import datetime

class GoogleScreenshotter:
    def __init__(self, chrome_profile_path=None):
        """
        Initialize with Chrome profile path
        
        Common Chrome profile locations:
        Windows: C:/Users/<USER>/AppData/Local/Google/Chrome/User Data
        Mac: ~/Library/Application Support/Google/Chrome
        Linux: ~/.config/google-chrome
        """
        self.chrome_profile_path = chrome_profile_path or self._get_default_chrome_profile()
        
    def _get_default_chrome_profile(self):
        """Get default Chrome profile path for current OS"""
        import platform
        system = platform.system()
        
        if system == "Windows":
            username = os.getenv('USERNAME')
            return f"C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"
        elif system == "Darwin":  # Mac
            return os.path.expanduser("~/Library/Application Support/Google/Chrome")
        else:  # Linux
            return os.path.expanduser("~/.config/google-chrome")
    
    async def search_and_screenshot(self, search_query, output_filename=None):
        """
        Search Google and take a screenshot using your Chrome profile
        """
        if not output_filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"google_search_{timestamp}.png"
        
        async with async_playwright() as p:
            # Launch Chrome with your existing profile
            browser = await p.chromium.launch_persistent_context(
                user_data_dir=self.chrome_profile_path,
                headless=False,  # Set to True for headless mode
                args=[
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # Get the first page (or create new one)
            if len(browser.pages) > 0:
                page = browser.pages[0]
            else:
                page = await browser.new_page()
            
            try:
                # Navigate to Google
                print("Navigating to Google...")
                await page.goto('https://www.google.com', wait_until='networkidle')
                
                # Find and fill search box
                print(f"Searching for: {search_query}")
                search_box = await page.wait_for_selector('input[name="q"]', timeout=10000)
                await search_box.fill(search_query)
                await search_box.press('Enter')
                
                # Wait for results to load
                print("Waiting for search results...")
                await page.wait_for_load_state('networkidle')
                await asyncio.sleep(2)  # Extra wait for dynamic content
                
                # Take screenshot
                print(f"Taking screenshot: {output_filename}")
                await page.screenshot(
                    path=output_filename,
                    full_page=True
                )
                
                print(f"Screenshot saved successfully: {output_filename}")
                return output_filename
                
            except Exception as e:
                print(f"Error during search: {e}")
                return None
            finally:
                await browser.close()

async def main():
    """Example usage"""
    screenshotter = GoogleScreenshotter()
    
    # Example searches
    searches = [
        "pizza restaurants in New York",
        "coffee shops in San Francisco", 
        "hotels in Paris",
        "dentist near me"
    ]
    
    for search in searches:
        print(f"\n--- Searching: {search} ---")
        result = await screenshotter.search_and_screenshot(search)
        if result:
            print(f"✅ Success: {result}")
        else:
            print("❌ Failed")
        
        # Wait between searches to be respectful
        await asyncio.sleep(3)

if __name__ == "__main__":
    # Run the example
    asyncio.run(main())
