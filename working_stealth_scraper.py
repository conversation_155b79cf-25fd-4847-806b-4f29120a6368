"""
Working Stealth Google Search Screenshot Automation
Based on successful simple test
"""

import asyncio
import os
import pandas as pd
import random
from datetime import datetime
from playwright.async_api import async_playwright
import re

class WorkingStealthScraper:
    def __init__(self, screenshots_folder="working_stealth_screenshots"):
        """Initialize the working stealth scraper"""
        self.screenshots_folder = screenshots_folder
        
        # Create screenshots folder
        os.makedirs(self.screenshots_folder, exist_ok=True)
        print(f"📁 Screenshots will be saved to: {self.screenshots_folder}")
        
        # Chrome profile path
        username = os.getenv('USERNAME')
        self.chrome_profile_path = f"C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"
        
        # Anti-detection configurations
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        
        # Stealth arguments that work
        self.stealth_args = [
            "--disable-blink-features=AutomationControlled",
            "--disable-features=VizDisplayCompositor",
            "--no-first-run",
            "--no-default-browser-check",
            "--exclude-switches=enable-automation",
            "--disable-automation"
        ]
        
        # Bot detection indicators
        self.bot_indicators = [
            'unusual traffic', 'captcha', 'not a robot', 'verify you are human',
            'suspicious activity', 'automated queries', 'recaptcha'
        ]
    
    def create_test_excel(self):
        """Create test Excel file with diverse searches"""
        search_data = [
            {'search_query': 'best pizza near me', 'category': 'Food', 'location': 'Local'},
            {'search_query': 'dentist office hours', 'category': 'Healthcare', 'location': 'Local'},
            {'search_query': 'coffee shop wifi', 'category': 'Food', 'location': 'Local'},
            {'search_query': 'auto repair reviews', 'category': 'Automotive', 'location': 'Local'},
            {'search_query': 'hair salon booking', 'category': 'Beauty', 'location': 'Local'},
            {'search_query': 'plumber emergency', 'category': 'Home Services', 'location': 'Local'},
            {'search_query': 'gym membership deals', 'category': 'Fitness', 'location': 'Local'},
            {'search_query': 'veterinarian near me', 'category': 'Pet Services', 'location': 'Local'},
            {'search_query': 'lawyer consultation', 'category': 'Legal', 'location': 'Local'},
            {'search_query': 'real estate agent', 'category': 'Real Estate', 'location': 'Local'},
            {'search_query': 'wedding photographer', 'category': 'Photography', 'location': 'Local'},
            {'search_query': 'accounting services', 'category': 'Professional', 'location': 'Local'},
            {'search_query': 'florist delivery', 'category': 'Retail', 'location': 'Local'},
            {'search_query': 'tutoring services', 'category': 'Education', 'location': 'Local'},
            {'search_query': 'hotel booking deals', 'category': 'Travel', 'location': 'Local'}
        ]
        
        df = pd.DataFrame(search_data)
        df['screenshot_taken'] = False
        df['screenshot_filename'] = ''
        df['timestamp'] = ''
        df['status'] = 'Pending'
        df['notes'] = ''
        df['bot_detected'] = False
        
        filename = f"working_stealth_searches_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df.to_excel(filename, sheet_name='Search Queries', index=False)
        
        print(f"✅ Created test Excel file: {filename}")
        print(f"📊 Total searches: {len(df)}")
        
        return filename, df
    
    def _sanitize_filename(self, text):
        """Create safe filename from search query"""
        safe_text = re.sub(r'[^\w\s-]', '', text)
        safe_text = re.sub(r'[-\s]+', '_', safe_text)
        return safe_text[:50]
    
    async def search_and_screenshot(self, search_query):
        """Perform single search with proven stealth techniques"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_query = self._sanitize_filename(search_query)
        screenshot_filename = f"{timestamp}_{safe_query}.png"
        screenshot_path = os.path.join(self.screenshots_folder, screenshot_filename)
        
        async with async_playwright() as p:
            browser = None
            try:
                print(f"🚀 Launching stealth browser for: {search_query}")
                
                # Launch browser with proven stealth settings
                browser = await p.chromium.launch_persistent_context(
                    user_data_dir=self.chrome_profile_path,
                    headless=False,
                    args=self.stealth_args,
                    ignore_default_args=["--enable-automation"]
                )
                
                # Get page
                if len(browser.pages) > 0:
                    page = browser.pages[0]
                else:
                    page = await browser.new_page()
                
                # Set stealth properties
                await page.add_init_script("""
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined,
                    });
                """)
                
                # Set random user agent
                user_agent = random.choice(self.user_agents)
                await page.set_extra_http_headers({
                    'User-Agent': user_agent
                })
                
                print(f"🕵️ Using User-Agent: {user_agent[:50]}...")
                
                # Navigate to Google
                print("🌐 Navigating to Google...")
                await page.goto('https://www.google.com', wait_until='networkidle')
                
                # Wait and check for bot detection
                await asyncio.sleep(2)
                page_content = await page.content()
                
                if any(indicator in page_content.lower() for indicator in self.bot_indicators):
                    print("🚨 Bot detection found on homepage!")
                    await page.screenshot(path=screenshot_path)
                    return False, screenshot_filename, "Bot detection on homepage", True
                
                print("✅ No bot detection on homepage")
                
                # Find search box
                search_box = None
                search_selectors = ['textarea[name="q"]', 'input[name="q"]']
                
                for selector in search_selectors:
                    try:
                        search_box = await page.wait_for_selector(selector, timeout=5000)
                        print(f"✅ Found search box with: {selector}")
                        break
                    except:
                        continue
                
                if not search_box:
                    await page.screenshot(path=screenshot_path)
                    return False, screenshot_filename, "Could not find search box", False
                
                # Perform search with human-like behavior
                print(f"⌨️ Typing search query...")
                await search_box.click()
                await asyncio.sleep(random.uniform(0.5, 1.0))
                
                # Type with random delays
                for char in search_query:
                    await search_box.type(char)
                    await asyncio.sleep(random.uniform(0.05, 0.15))
                
                await asyncio.sleep(random.uniform(0.5, 1.0))
                await search_box.press('Enter')
                
                # Wait for results
                print("⏳ Waiting for search results...")
                await page.wait_for_load_state('networkidle', timeout=15000)
                await asyncio.sleep(3)
                
                # Check search results for bot detection
                search_content = await page.content()
                bot_detected = any(indicator in search_content.lower() for indicator in self.bot_indicators)
                
                # Take screenshot
                await page.screenshot(path=screenshot_path, full_page=True)
                
                if bot_detected:
                    print("🚨 Bot detection found in search results!")
                    return False, screenshot_filename, "Bot detection in search results", True
                else:
                    print("✅ Search successful - no bot detection!")
                    return True, screenshot_filename, "Success", False
                
            except Exception as e:
                error_msg = str(e)
                print(f"❌ Error for '{search_query}': {error_msg}")
                return False, "", error_msg, False
            finally:
                if browser:
                    await browser.close()
                    # Wait before next browser instance
                    await asyncio.sleep(random.uniform(3, 5))
    
    async def process_searches(self, excel_file, df, delay_between_searches=12):
        """Process all searches with tracking"""
        total_searches = len(df)
        successful = 0
        failed = 0
        bot_detected_count = 0
        
        print(f"\n🎯 Starting to process {total_searches} searches...")
        print(f"⏱️  Delay between searches: {delay_between_searches} seconds")
        print("-" * 60)
        
        for index, row in df.iterrows():
            search_query = row['search_query']
            print(f"\n[{index + 1}/{total_searches}] Processing: {search_query}")
            
            # Perform search
            success, filename, error, bot_detected = await self.search_and_screenshot(search_query)
            
            # Update DataFrame
            df.at[index, 'screenshot_taken'] = success
            df.at[index, 'screenshot_filename'] = filename
            df.at[index, 'timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            df.at[index, 'bot_detected'] = bot_detected
            
            if success:
                df.at[index, 'status'] = 'Completed'
                df.at[index, 'notes'] = 'Screenshot taken successfully'
                successful += 1
            else:
                if bot_detected:
                    df.at[index, 'status'] = 'Bot Detected'
                    bot_detected_count += 1
                else:
                    df.at[index, 'status'] = 'Failed'
                df.at[index, 'notes'] = error
                failed += 1
            
            # Save progress
            df.to_excel(excel_file, sheet_name='Search Queries', index=False)
            
            # Wait between searches
            if index < total_searches - 1:
                actual_delay = random.uniform(delay_between_searches * 0.8, delay_between_searches * 1.2)
                print(f"⏳ Waiting {actual_delay:.1f} seconds before next search...")
                await asyncio.sleep(actual_delay)
        
        # Calculate success rate
        success_rate = (successful / total_searches) * 100
        bot_detection_rate = (bot_detected_count / total_searches) * 100
        
        # Final summary
        print("\n" + "=" * 60)
        print("🎉 PROCESSING COMPLETE!")
        print(f"✅ Successful: {successful}")
        print(f"🚨 Bot Detected: {bot_detected_count}")
        print(f"❌ Other Failures: {failed}")
        print(f"📊 Total: {total_searches}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        print(f"🚨 Bot Detection Rate: {bot_detection_rate:.1f}%")
        print(f"📁 Screenshots saved in: {self.screenshots_folder}")
        print("=" * 60)
        
        return success_rate, bot_detection_rate

async def main():
    """Main function"""
    print("🕵️ Working Stealth Google Search Screenshot Automation")
    print("=" * 60)
    
    scraper = WorkingStealthScraper()
    
    # Create test data
    excel_file, df = scraper.create_test_excel()
    
    # Process searches
    success_rate, bot_rate = await scraper.process_searches(excel_file, df)
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    print(f"🚨 Bot Detection Rate: {bot_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 EXCELLENT! Target achieved!")
    elif success_rate >= 80:
        print("👍 GOOD! Close to target")
    else:
        print("⚠️ NEEDS IMPROVEMENT")

if __name__ == "__main__":
    asyncio.run(main())
