"""
Simple stealth test to debug the issue
"""

import asyncio
import os
import random
from playwright.async_api import async_playwright

async def simple_stealth_test():
    """Simple test with stealth features"""
    
    print("🚀 Starting simple stealth test...")
    
    # Chrome profile path
    username = os.getenv('USERNAME')
    chrome_profile_path = f"C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"
    
    # User agents
    user_agents = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"
    ]
    
    # Stealth arguments
    stealth_args = [
        "--disable-blink-features=AutomationControlled",
        "--disable-features=VizDisplayCompositor",
        "--no-first-run",
        "--no-default-browser-check",
        "--exclude-switches=enable-automation",
        "--disable-automation"
    ]
    
    async with async_playwright() as p:
        try:
            print("🔧 Launching Chrome with stealth settings...")
            
            browser = await p.chromium.launch_persistent_context(
                user_data_dir=chrome_profile_path,
                headless=False,
                args=stealth_args,
                ignore_default_args=["--enable-automation"]
            )
            
            print("✅ Chrome launched successfully")
            
            # Get page
            if len(browser.pages) > 0:
                page = browser.pages[0]
            else:
                page = await browser.new_page()
            
            print("📄 Page created")
            
            # Set stealth properties
            await page.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
            
            # Set random user agent
            user_agent = random.choice(user_agents)
            await page.set_extra_http_headers({
                'User-Agent': user_agent
            })
            
            print(f"🕵️ Set User-Agent: {user_agent[:50]}...")
            
            # Navigate to Google
            print("🌐 Navigating to Google...")
            await page.goto('https://www.google.com', wait_until='networkidle')
            
            print("⏳ Waiting 3 seconds...")
            await asyncio.sleep(3)
            
            # Take a test screenshot
            screenshot_path = "stealth_test_screenshot.png"
            await page.screenshot(path=screenshot_path, full_page=True)
            print(f"📸 Screenshot saved: {screenshot_path}")
            
            # Check page content for bot detection
            page_content = await page.content()
            
            bot_indicators = ['unusual traffic', 'captcha', 'not a robot', 'verify you are human']
            bot_detected = any(indicator in page_content.lower() for indicator in bot_indicators)
            
            if bot_detected:
                print("🚨 Bot detection found in page content!")
            else:
                print("✅ No bot detection found - stealth working!")
            
            # Try a simple search
            print("🔍 Attempting search...")
            
            try:
                # Find search box
                search_selectors = ['textarea[name="q"]', 'input[name="q"]']
                search_box = None
                
                for selector in search_selectors:
                    try:
                        search_box = await page.wait_for_selector(selector, timeout=5000)
                        print(f"✅ Found search box with: {selector}")
                        break
                    except:
                        continue
                
                if search_box:
                    # Type search query
                    await search_box.click()
                    await asyncio.sleep(1)
                    await search_box.type("best pizza near me")
                    await asyncio.sleep(1)
                    await search_box.press('Enter')
                    
                    print("⏳ Waiting for search results...")
                    await page.wait_for_load_state('networkidle', timeout=15000)
                    await asyncio.sleep(3)
                    
                    # Take search results screenshot
                    search_screenshot = "stealth_search_results.png"
                    await page.screenshot(path=search_screenshot, full_page=True)
                    print(f"📸 Search results screenshot: {search_screenshot}")
                    
                    # Check search results
                    search_content = await page.content()
                    search_bot_detected = any(indicator in search_content.lower() for indicator in bot_indicators)
                    
                    if search_bot_detected:
                        print("🚨 Bot detection found in search results!")
                    else:
                        print("✅ Search successful - no bot detection!")
                
                else:
                    print("❌ Could not find search box")
                    
            except Exception as search_error:
                print(f"❌ Search failed: {search_error}")
            
            print("⏳ Keeping browser open for 5 seconds...")
            await asyncio.sleep(5)
            
            await browser.close()
            print("✅ Test completed successfully")
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    print("🕵️ Simple Stealth Test")
    print("=" * 30)
    asyncio.run(simple_stealth_test())
