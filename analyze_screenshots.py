"""
Screenshot Analyzer - Analyze screenshots to understand bot detection patterns
"""

import os
import pandas as pd
from PIL import Image
import pytesseract
import re
from datetime import datetime

class ScreenshotAnalyzer:
    def __init__(self, screenshots_folder="final_stealth_screenshots"):
        self.screenshots_folder = screenshots_folder
        self.bot_detection_keywords = [
            'unusual traffic', 'captcha', 'not a robot', 'verify you are human',
            'suspicious activity', 'automated queries', 'recaptcha', 'prove you are human',
            'security check', 'verify that you are not a robot', 'before we continue',
            'solve this puzzle', 'i\'m not a robot', 'verify your identity'
        ]
        
    def analyze_screenshot(self, screenshot_path):
        """Analyze a single screenshot for bot detection"""
        try:
            if not os.path.exists(screenshot_path):
                return False, "Screenshot file not found", ""
            
            # Open and analyze image
            image = Image.open(screenshot_path)
            
            # Extract text using OCR
            text = pytesseract.image_to_string(image).lower()
            
            # Check for bot detection indicators
            bot_detected = False
            detected_keywords = []
            
            for keyword in self.bot_detection_keywords:
                if keyword in text:
                    bot_detected = True
                    detected_keywords.append(keyword)
            
            # Additional checks
            if 'google' not in text and len(text.strip()) < 50:
                return False, "Page didn't load properly", text[:200]
            
            if bot_detected:
                return True, f"Bot detection found: {', '.join(detected_keywords)}", text[:500]
            else:
                # Check if it looks like a successful search results page
                success_indicators = ['search', 'results', 'about', 'web', 'images', 'news']
                success_count = sum(1 for indicator in success_indicators if indicator in text)
                
                if success_count >= 3:
                    return False, "Successful search results page", text[:200]
                else:
                    return False, "Unknown page content", text[:200]
                    
        except Exception as e:
            return False, f"Error analyzing screenshot: {str(e)}", ""
    
    def analyze_all_screenshots(self, excel_file):
        """Analyze all screenshots and update Excel file"""
        if not os.path.exists(excel_file):
            print(f"❌ Excel file not found: {excel_file}")
            return
        
        # Read Excel file
        df = pd.read_excel(excel_file)
        
        print(f"📊 Analyzing {len(df)} screenshots...")
        print("-" * 60)
        
        analyzed_count = 0
        bot_detected_count = 0
        successful_count = 0
        
        # Add new columns for analysis
        if 'ocr_bot_detected' not in df.columns:
            df['ocr_bot_detected'] = False
        if 'ocr_analysis' not in df.columns:
            df['ocr_analysis'] = ''
        if 'ocr_text_sample' not in df.columns:
            df['ocr_text_sample'] = ''
        
        for index, row in df.iterrows():
            screenshot_filename = row['screenshot_filename']
            search_query = row['search_query']
            
            if screenshot_filename and screenshot_filename.strip():
                screenshot_path = os.path.join(self.screenshots_folder, screenshot_filename)
                
                print(f"[{index + 1}/{len(df)}] Analyzing: {search_query}")
                print(f"   Screenshot: {screenshot_filename}")
                
                bot_detected, analysis, text_sample = self.analyze_screenshot(screenshot_path)
                
                # Update DataFrame
                df.at[index, 'ocr_bot_detected'] = bot_detected
                df.at[index, 'ocr_analysis'] = analysis
                df.at[index, 'ocr_text_sample'] = text_sample
                
                analyzed_count += 1
                
                if bot_detected:
                    bot_detected_count += 1
                    print(f"   🚨 BOT DETECTION: {analysis}")
                else:
                    successful_count += 1
                    print(f"   ✅ SUCCESS: {analysis}")
            else:
                print(f"[{index + 1}/{len(df)}] No screenshot for: {search_query}")
                df.at[index, 'ocr_analysis'] = 'No screenshot available'
        
        # Save updated Excel file
        output_file = excel_file.replace('.xlsx', '_analyzed.xlsx')
        df.to_excel(output_file, sheet_name='Search Queries', index=False)
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 SCREENSHOT ANALYSIS COMPLETE!")
        print(f"📸 Screenshots analyzed: {analyzed_count}")
        print(f"✅ Successful: {successful_count}")
        print(f"🚨 Bot detected: {bot_detected_count}")
        
        if analyzed_count > 0:
            success_rate = (successful_count / analyzed_count) * 100
            bot_rate = (bot_detected_count / analyzed_count) * 100
            print(f"📈 OCR Success Rate: {success_rate:.1f}%")
            print(f"🚨 OCR Bot Detection Rate: {bot_rate:.1f}%")
        
        print(f"💾 Analysis saved to: {output_file}")
        print("=" * 60)
        
        return df
    
    def generate_report(self, df):
        """Generate detailed analysis report"""
        report = []
        report.append("🔍 DETAILED SCREENSHOT ANALYSIS REPORT")
        report.append("=" * 50)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Overall statistics
        total = len(df)
        successful = len(df[df['ocr_bot_detected'] == False])
        bot_detected = len(df[df['ocr_bot_detected'] == True])
        
        report.append("📊 OVERALL STATISTICS:")
        report.append(f"Total searches: {total}")
        report.append(f"Successful: {successful} ({successful/total*100:.1f}%)")
        report.append(f"Bot detected: {bot_detected} ({bot_detected/total*100:.1f}%)")
        report.append("")
        
        # Bot detection patterns
        if bot_detected > 0:
            report.append("🚨 BOT DETECTION PATTERNS:")
            bot_df = df[df['ocr_bot_detected'] == True]
            
            # Count detection reasons
            detection_reasons = {}
            for analysis in bot_df['ocr_analysis']:
                if 'Bot detection found:' in analysis:
                    keywords = analysis.replace('Bot detection found:', '').strip()
                    for keyword in keywords.split(','):
                        keyword = keyword.strip()
                        if keyword:
                            detection_reasons[keyword] = detection_reasons.get(keyword, 0) + 1
            
            for reason, count in sorted(detection_reasons.items(), key=lambda x: x[1], reverse=True):
                report.append(f"  • {reason}: {count} times")
            report.append("")
        
        # Success patterns
        if successful > 0:
            report.append("✅ SUCCESS PATTERNS:")
            success_df = df[df['ocr_bot_detected'] == False]
            
            # Analyze successful user agents
            user_agents = success_df['user_agent_used'].value_counts()
            report.append("Most successful user agents:")
            for ua, count in user_agents.head(3).items():
                if ua and ua.strip():
                    report.append(f"  • {ua[:50]}...: {count} times")
            report.append("")
        
        # Recommendations
        report.append("💡 RECOMMENDATIONS:")
        
        if bot_detected / total > 0.5:
            report.append("  • High bot detection rate - consider:")
            report.append("    - Longer delays between searches")
            report.append("    - Different user agent rotation strategy")
            report.append("    - Additional stealth techniques")
        
        if successful > 0:
            report.append("  • Some searches are successful - optimize:")
            report.append("    - Use patterns from successful searches")
            report.append("    - Replicate successful user agents")
            report.append("    - Maintain successful timing patterns")
        
        report.append("")
        report.append("🔧 NEXT STEPS:")
        report.append("  1. Implement longer delays (60+ seconds)")
        report.append("  2. Add proxy rotation")
        report.append("  3. Implement session management")
        report.append("  4. Add mouse movement simulation")
        report.append("  5. Consider using residential proxies")
        
        return "\n".join(report)

def main():
    """Main function to analyze screenshots"""
    print("🔍 Screenshot Analysis Tool")
    print("=" * 40)
    
    analyzer = ScreenshotAnalyzer()
    
    # Find the most recent Excel file
    excel_files = [f for f in os.listdir('.') if f.startswith('final_stealth_searches_') and f.endswith('.xlsx')]
    
    if not excel_files:
        print("❌ No Excel files found!")
        return
    
    # Use the most recent file
    excel_file = sorted(excel_files)[-1]
    print(f"📊 Analyzing Excel file: {excel_file}")
    
    # Analyze all screenshots
    df = analyzer.analyze_all_screenshots(excel_file)
    
    if df is not None:
        # Generate detailed report
        report = analyzer.generate_report(df)
        
        # Save report
        report_file = f"analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        print("\n" + report)

if __name__ == "__main__":
    main()
