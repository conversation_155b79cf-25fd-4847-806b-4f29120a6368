"""
Create Excel file with dummy search queries for Google screenshot automation
"""

import pandas as pd
from datetime import datetime

def create_search_excel():
    """Create Excel file with dummy search queries"""
    
    # Dummy search data - mix of different types of searches
    search_data = [
        {
            'search_query': 'pizza restaurants in New York',
            'category': 'Food & Dining',
            'location': 'New York',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'dentist near me Chicago',
            'category': 'Healthcare',
            'location': 'Chicago',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'coffee shops in San Francisco',
            'category': 'Food & Dining',
            'location': 'San Francisco',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'hotels in Miami Beach',
            'category': 'Travel & Lodging',
            'location': 'Miami',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'car repair shops Boston',
            'category': 'Automotive',
            'location': 'Boston',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'hair salons Los Angeles',
            'category': 'Beauty & Personal Care',
            'location': 'Los Angeles',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'gyms and fitness centers Seattle',
            'category': 'Health & Fitness',
            'location': 'Seattle',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'plumbers in Phoenix Arizona',
            'category': 'Home Services',
            'location': 'Phoenix',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'veterinarians in Austin Texas',
            'category': 'Pet Services',
            'location': 'Austin',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'lawyers in Denver Colorado',
            'category': 'Legal Services',
            'location': 'Denver',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'real estate agents Portland Oregon',
            'category': 'Real Estate',
            'location': 'Portland',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'wedding photographers Nashville',
            'category': 'Photography',
            'location': 'Nashville',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'accounting services Atlanta',
            'category': 'Professional Services',
            'location': 'Atlanta',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'florists in Las Vegas Nevada',
            'category': 'Retail & Shopping',
            'location': 'Las Vegas',
            'search_type': 'Local Business'
        },
        {
            'search_query': 'tutoring services Minneapolis',
            'category': 'Education',
            'location': 'Minneapolis',
            'search_type': 'Local Business'
        }
    ]
    
    # Create DataFrame
    df = pd.DataFrame(search_data)
    
    # Add additional columns for tracking
    df['screenshot_taken'] = False
    df['screenshot_filename'] = ''
    df['timestamp'] = ''
    df['status'] = 'Pending'
    df['notes'] = ''
    
    # Save to Excel
    filename = f"google_search_queries_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    # Create Excel writer with formatting
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Search Queries', index=False)
        
        # Get the workbook and worksheet
        workbook = writer.book
        worksheet = writer.sheets['Search Queries']
        
        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    print(f"✅ Excel file created: {filename}")
    print(f"📊 Total searches: {len(df)}")
    print("\nSearch categories:")
    for category in df['category'].value_counts().items():
        print(f"  - {category[0]}: {category[1]} searches")
    
    return filename, df

if __name__ == "__main__":
    create_search_excel()
