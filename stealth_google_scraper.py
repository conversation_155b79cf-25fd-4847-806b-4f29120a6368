"""
Advanced Anti-Detection Google Search Screenshot Automation
Implements latest 2024 anti-detection techniques
"""

import asyncio
import os
import pandas as pd
import random
import json
from datetime import datetime
from playwright.async_api import async_playwright
import re

class StealthGoogleScraper:
    def __init__(self, excel_file, chrome_profile_path=None, screenshots_folder="stealth_screenshots"):
        """
        Initialize the stealth scraper with advanced anti-detection
        """
        self.excel_file = excel_file
        self.chrome_profile_path = chrome_profile_path or self._get_default_chrome_profile()
        self.screenshots_folder = screenshots_folder
        self.df = None
        
        # Create screenshots folder
        os.makedirs(self.screenshots_folder, exist_ok=True)
        print(f"📁 Screenshots will be saved to: {self.screenshots_folder}")
        
        # Anti-detection configurations
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"
        ]
        
        self.viewports = [
            {"width": 1920, "height": 1080},
            {"width": 1366, "height": 768},
            {"width": 1536, "height": 864},
            {"width": 1440, "height": 900},
            {"width": 1280, "height": 720}
        ]
        
    def _get_default_chrome_profile(self):
        """Get default Chrome profile path"""
        import platform
        system = platform.system()
        
        if system == "Windows":
            username = os.getenv('USERNAME')
            return f"C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"
        elif system == "Darwin":
            return os.path.expanduser("~/Library/Application Support/Google/Chrome")
        else:
            return os.path.expanduser("~/.config/google-chrome")
    
    def _get_stealth_args(self):
        """Get comprehensive stealth browser arguments"""
        return [
            # Basic stealth
            "--disable-blink-features=AutomationControlled",
            "--disable-features=VizDisplayCompositor",
            "--disable-web-security",
            "--disable-features=TranslateUI",
            "--disable-ipc-flooding-protection",
            
            # Remove automation indicators
            "--exclude-switches=enable-automation",
            "--disable-automation",
            "--disable-extensions-except",
            "--disable-plugins-discovery",
            
            # Mimic real browser behavior
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-default-apps",
            "--disable-popup-blocking",
            
            # Performance and stability
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-background-timer-throttling",
            "--disable-features=TranslateUI",
            
            # Additional stealth
            "--disable-component-extensions-with-background-pages",
            "--disable-dev-shm-usage",
            "--no-sandbox",
            "--disable-setuid-sandbox"
        ]
    
    async def _setup_stealth_page(self, page):
        """Configure page with stealth settings"""
        # Remove webdriver property
        await page.add_init_script("""
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        """)
        
        # Override permissions
        await page.add_init_script("""
            const originalQuery = window.navigator.permissions.query;
            return window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        """)
        
        # Override plugins
        await page.add_init_script("""
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
        """)
        
        # Override languages
        await page.add_init_script("""
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
        """)
        
        # Set random viewport
        viewport = random.choice(self.viewports)
        await page.set_viewport_size(viewport["width"], viewport["height"])
        
        # Set random user agent
        user_agent = random.choice(self.user_agents)
        await page.set_extra_http_headers({
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        })
        
        print(f"🎭 Using viewport: {viewport['width']}x{viewport['height']}")
        print(f"🕵️ Using User-Agent: {user_agent[:50]}...")
    
    def load_excel(self):
        """Load search queries from Excel file"""
        try:
            self.df = pd.read_excel(self.excel_file, sheet_name='Search Queries')
            print(f"📊 Loaded {len(self.df)} search queries from {self.excel_file}")
            return True
        except Exception as e:
            print(f"❌ Error loading Excel file: {e}")
            return False
    
    def save_excel(self):
        """Save updated DataFrame back to Excel"""
        try:
            with pd.ExcelWriter(self.excel_file, engine='openpyxl') as writer:
                self.df.to_excel(writer, sheet_name='Search Queries', index=False)
                
                # Auto-adjust column widths
                workbook = writer.book
                worksheet = writer.sheets['Search Queries']
                
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            print(f"💾 Excel file updated: {self.excel_file}")
            return True
        except Exception as e:
            print(f"❌ Error saving Excel file: {e}")
            return False
    
    def _sanitize_filename(self, text):
        """Create safe filename from search query"""
        safe_text = re.sub(r'[^\w\s-]', '', text)
        safe_text = re.sub(r'[-\s]+', '_', safe_text)
        return safe_text[:50]
    
    async def _human_like_delay(self, min_seconds=1, max_seconds=3):
        """Add human-like random delays"""
        delay = random.uniform(min_seconds, max_seconds)
        await asyncio.sleep(delay)
    
    async def search_and_screenshot(self, search_query, row_index):
        """
        Perform single search with advanced stealth techniques
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_query = self._sanitize_filename(search_query)
        screenshot_filename = f"{timestamp}_{safe_query}.png"
        screenshot_path = os.path.join(self.screenshots_folder, screenshot_filename)
        
        async with async_playwright() as p:
            browser = None
            try:
                print(f"🚀 Launching stealth browser for: {search_query}")
                
                # Launch browser with stealth arguments
                browser = await p.chromium.launch_persistent_context(
                    user_data_dir=self.chrome_profile_path,
                    headless=False,
                    args=self._get_stealth_args(),
                    ignore_default_args=["--enable-automation"],
                    slow_mo=random.randint(50, 150)  # Random slow motion
                )
                
                # Get or create page
                if len(browser.pages) > 0:
                    page = browser.pages[0]
                else:
                    page = await browser.new_page()
                
                # Setup stealth configurations
                await self._setup_stealth_page(page)
                
                # Human-like delay before navigation
                await self._human_like_delay(1, 2)
                
                # Navigate to Google with random delay
                print("🌐 Navigating to Google...")
                await page.goto('https://www.google.com', wait_until='networkidle')
                
                # Random delay after page load
                await self._human_like_delay(2, 4)
                
                # Find search box with multiple attempts
                search_box = None
                selectors = [
                    'textarea[name="q"]',
                    'input[name="q"]',
                    '[data-ved] input',
                    'input[type="text"]',
                    '#APjFqb'
                ]
                
                for selector in selectors:
                    try:
                        search_box = await page.wait_for_selector(selector, timeout=5000)
                        print(f"✅ Found search box with selector: {selector}")
                        break
                    except:
                        continue
                
                if not search_box:
                    # Take debug screenshot
                    debug_path = os.path.join(self.screenshots_folder, f"debug_{timestamp}.png")
                    await page.screenshot(path=debug_path)
                    return False, "", f"Could not find search box. Debug screenshot: debug_{timestamp}.png"
                
                # Human-like typing
                print(f"⌨️ Typing search query...")
                await search_box.click()
                await self._human_like_delay(0.5, 1)
                
                # Type with random delays between characters
                for char in search_query:
                    await search_box.type(char)
                    await asyncio.sleep(random.uniform(0.05, 0.15))
                
                await self._human_like_delay(0.5, 1.5)
                
                # Press Enter
                await search_box.press('Enter')
                
                # Wait for results with timeout
                print("⏳ Waiting for search results...")
                try:
                    await page.wait_for_selector('#search', timeout=15000)
                except:
                    # If main search results don't load, wait for any content
                    await page.wait_for_load_state('networkidle', timeout=15000)
                
                # Additional wait for dynamic content
                await self._human_like_delay(3, 5)
                
                # Check if we got a CAPTCHA or bot detection page
                page_content = await page.content()
                if any(indicator in page_content.lower() for indicator in [
                    'unusual traffic', 'captcha', 'not a robot', 'verify you are human',
                    'suspicious activity', 'automated queries'
                ]):
                    print("🚨 Bot detection page detected!")
                    await page.screenshot(path=screenshot_path)
                    return False, screenshot_filename, "Bot detection page encountered"
                
                # Take screenshot
                print(f"📸 Taking screenshot...")
                await page.screenshot(path=screenshot_path, full_page=True)
                
                print(f"✅ Screenshot saved: {screenshot_filename}")
                return True, screenshot_filename, ""
                
            except Exception as e:
                error_msg = str(e)
                print(f"❌ Error for '{search_query}': {error_msg}")
                return False, "", error_msg
            finally:
                if browser:
                    await browser.close()
                    # Wait before next browser instance
                    await self._human_like_delay(2, 4)

    async def process_all_searches(self, delay_between_searches=8):
        """
        Process all searches with enhanced stealth
        """
        if not self.load_excel():
            return

        total_searches = len(self.df)
        successful = 0
        failed = 0
        bot_detected = 0

        print(f"\n🎯 Starting stealth processing of {total_searches} searches...")
        print(f"⏱️  Delay between searches: {delay_between_searches} seconds")
        print(f"📁 Screenshots folder: {self.screenshots_folder}")
        print("-" * 60)

        for index, row in self.df.iterrows():
            search_query = row['search_query']
            print(f"\n[{index + 1}/{total_searches}] Processing: {search_query}")

            # Perform search and screenshot
            success, filename, error = await self.search_and_screenshot(search_query, index)

            # Update DataFrame
            self.df.at[index, 'screenshot_taken'] = success
            self.df.at[index, 'screenshot_filename'] = filename
            self.df.at[index, 'timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            if success:
                self.df.at[index, 'status'] = 'Completed'
                self.df.at[index, 'notes'] = 'Screenshot taken successfully'
                successful += 1
            else:
                if 'bot detection' in error.lower():
                    self.df.at[index, 'status'] = 'Bot Detected'
                    bot_detected += 1
                else:
                    self.df.at[index, 'status'] = 'Failed'
                self.df.at[index, 'notes'] = error
                failed += 1

            # Save progress after each search
            self.save_excel()

            # Wait between searches with random variation
            if index < total_searches - 1:
                actual_delay = random.uniform(delay_between_searches * 0.8, delay_between_searches * 1.2)
                print(f"⏳ Waiting {actual_delay:.1f} seconds before next search...")
                await asyncio.sleep(actual_delay)

        # Calculate success rate
        success_rate = (successful / total_searches) * 100

        # Final summary
        print("\n" + "=" * 60)
        print("🎉 STEALTH PROCESSING COMPLETE!")
        print(f"✅ Successful: {successful}")
        print(f"🚨 Bot Detected: {bot_detected}")
        print(f"❌ Other Failures: {failed}")
        print(f"📊 Total: {total_searches}")
        print(f"📈 Success Rate: {success_rate:.1f}%")
        print(f"📁 Screenshots saved in: {self.screenshots_folder}")
        print(f"📋 Excel file updated: {self.excel_file}")

        if success_rate < 90:
            print("\n⚠️  Success rate below 90%. Consider:")
            print("   - Increasing delays between searches")
            print("   - Using different Chrome profile")
            print("   - Running fewer searches per session")

        print("=" * 60)
        return success_rate

async def main():
    """Main function with enhanced dummy data"""

    # Create enhanced Excel file with more diverse searches
    print("📝 Creating enhanced Excel file with dummy search queries...")
    try:
        from create_enhanced_excel import create_enhanced_search_excel
        excel_filename, _ = create_enhanced_search_excel()
    except ImportError:
        print("❌ Could not import create_enhanced_excel, using fallback...")
        # Fallback to basic Excel creation
        excel_filename = "test_searches.xlsx"
        import pandas as pd
        basic_data = [
            {'search_query': 'best pizza near me', 'category': 'Food', 'location': 'Local', 'search_type': 'Local'},
            {'search_query': 'dentist office hours', 'category': 'Healthcare', 'location': 'Local', 'search_type': 'Local'},
            {'search_query': 'coffee shop wifi', 'category': 'Food', 'location': 'Local', 'search_type': 'Local'},
            {'search_query': 'auto repair reviews', 'category': 'Automotive', 'location': 'Local', 'search_type': 'Local'},
            {'search_query': 'hair salon booking', 'category': 'Beauty', 'location': 'Local', 'search_type': 'Local'}
        ]
        df = pd.DataFrame(basic_data)
        df['screenshot_taken'] = False
        df['screenshot_filename'] = ''
        df['timestamp'] = ''
        df['status'] = 'Pending'
        df['notes'] = ''
        df.to_excel(excel_filename, sheet_name='Search Queries', index=False)
        print(f"✅ Created fallback Excel file: {excel_filename}")

    print(f"\n🔧 Setting up stealth scraper...")

    # Initialize stealth scraper
    scraper = StealthGoogleScraper(
        excel_file=excel_filename,
        screenshots_folder="stealth_screenshots"
    )

    # Process searches with longer delays for better success rate
    success_rate = await scraper.process_all_searches(delay_between_searches=10)

    return success_rate

if __name__ == "__main__":
    print("🕵️ Advanced Stealth Google Search Screenshot Automation")
    print("=" * 60)
    asyncio.run(main())
