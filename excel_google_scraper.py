"""
Google Search Screenshot Automation using Excel file and Chrome Profile
Reads search queries from Excel, takes screenshots, and updates the Excel file
"""

import asyncio
import os
import pandas as pd
from datetime import datetime
from playwright.async_api import async_playwright
import re

class ExcelGoogleScraper:
    def __init__(self, excel_file, chrome_profile_path=None, screenshots_folder="screenshots"):
        """
        Initialize the scraper
        
        Args:
            excel_file: Path to Excel file with search queries
            chrome_profile_path: Path to Chrome profile (auto-detected if None)
            screenshots_folder: Folder to save screenshots
        """
        self.excel_file = excel_file
        self.chrome_profile_path = chrome_profile_path or self._get_default_chrome_profile()
        self.screenshots_folder = screenshots_folder
        self.df = None
        
        # Create screenshots folder if it doesn't exist
        os.makedirs(self.screenshots_folder, exist_ok=True)
        print(f"📁 Screenshots will be saved to: {self.screenshots_folder}")
        
    def _get_default_chrome_profile(self):
        """Get default Chrome profile path for current OS"""
        import platform
        system = platform.system()
        
        if system == "Windows":
            username = os.getenv('USERNAME')
            return f"C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"
        elif system == "Darwin":  # Mac
            return os.path.expanduser("~/Library/Application Support/Google/Chrome")
        else:  # Linux
            return os.path.expanduser("~/.config/google-chrome")
    
    def load_excel(self):
        """Load search queries from Excel file"""
        try:
            self.df = pd.read_excel(self.excel_file, sheet_name='Search Queries')
            print(f"📊 Loaded {len(self.df)} search queries from {self.excel_file}")
            return True
        except Exception as e:
            print(f"❌ Error loading Excel file: {e}")
            return False
    
    def save_excel(self):
        """Save updated DataFrame back to Excel"""
        try:
            with pd.ExcelWriter(self.excel_file, engine='openpyxl') as writer:
                self.df.to_excel(writer, sheet_name='Search Queries', index=False)
                
                # Auto-adjust column widths
                workbook = writer.book
                worksheet = writer.sheets['Search Queries']
                
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            print(f"💾 Excel file updated: {self.excel_file}")
            return True
        except Exception as e:
            print(f"❌ Error saving Excel file: {e}")
            return False
    
    def _sanitize_filename(self, text):
        """Create safe filename from search query"""
        # Remove special characters and limit length
        safe_text = re.sub(r'[^\w\s-]', '', text)
        safe_text = re.sub(r'[-\s]+', '_', safe_text)
        return safe_text[:50]  # Limit to 50 characters
    
    async def search_and_screenshot(self, search_query, row_index):
        """
        Perform single search and take screenshot
        
        Args:
            search_query: The search query string
            row_index: Index of the row in DataFrame
            
        Returns:
            tuple: (success, screenshot_filename, error_message)
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_query = self._sanitize_filename(search_query)
        screenshot_filename = f"{timestamp}_{safe_query}.png"
        screenshot_path = os.path.join(self.screenshots_folder, screenshot_filename)
        
        async with async_playwright() as p:
            browser = None
            try:
                # Launch Chrome with profile
                print(f"🚀 Launching Chrome for: {search_query}")
                browser = await p.chromium.launch_persistent_context(
                    user_data_dir=self.chrome_profile_path,
                    headless=False,  # Set to True for headless mode
                    args=[
                        '--no-first-run',
                        '--no-default-browser-check',
                        '--disable-blink-features=AutomationControlled',
                    ]
                )
                
                # Get or create page
                if len(browser.pages) > 0:
                    page = browser.pages[0]
                else:
                    page = await browser.new_page()
                
                # Navigate to Google
                await page.goto('https://www.google.com', wait_until='networkidle')
                await asyncio.sleep(2)
                
                # Find search box
                search_box = None
                selectors = [
                    'input[name="q"]',
                    'textarea[name="q"]', 
                    '[data-ved] input',
                    'input[type="text"]',
                    '#APjFqb'
                ]
                
                for selector in selectors:
                    try:
                        search_box = await page.wait_for_selector(selector, timeout=3000)
                        break
                    except:
                        continue
                
                if not search_box:
                    return False, "", "Could not find search box"
                
                # Perform search
                await search_box.fill(search_query)
                await search_box.press('Enter')
                
                # Wait for results
                await page.wait_for_load_state('networkidle')
                await asyncio.sleep(3)  # Extra wait for dynamic content
                
                # Take screenshot
                await page.screenshot(path=screenshot_path, full_page=True)
                
                print(f"✅ Screenshot saved: {screenshot_filename}")
                return True, screenshot_filename, ""
                
            except Exception as e:
                error_msg = str(e)
                print(f"❌ Error for '{search_query}': {error_msg}")
                return False, "", error_msg
            finally:
                if browser:
                    await browser.close()
    
    async def process_all_searches(self, delay_between_searches=5):
        """
        Process all searches from Excel file
        
        Args:
            delay_between_searches: Seconds to wait between searches
        """
        if not self.load_excel():
            return
        
        total_searches = len(self.df)
        successful = 0
        failed = 0
        
        print(f"\n🎯 Starting to process {total_searches} searches...")
        print(f"⏱️  Delay between searches: {delay_between_searches} seconds")
        print(f"📁 Screenshots folder: {self.screenshots_folder}")
        print("-" * 60)
        
        for index, row in self.df.iterrows():
            search_query = row['search_query']
            print(f"\n[{index + 1}/{total_searches}] Processing: {search_query}")
            
            # Perform search and screenshot
            success, filename, error = await self.search_and_screenshot(search_query, index)
            
            # Update DataFrame
            self.df.at[index, 'screenshot_taken'] = success
            self.df.at[index, 'screenshot_filename'] = filename
            self.df.at[index, 'timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            if success:
                self.df.at[index, 'status'] = 'Completed'
                self.df.at[index, 'notes'] = 'Screenshot taken successfully'
                successful += 1
            else:
                self.df.at[index, 'status'] = 'Failed'
                self.df.at[index, 'notes'] = error
                failed += 1
            
            # Save progress after each search
            self.save_excel()
            
            # Wait between searches (except for the last one)
            if index < total_searches - 1:
                print(f"⏳ Waiting {delay_between_searches} seconds before next search...")
                await asyncio.sleep(delay_between_searches)
        
        # Final summary
        print("\n" + "=" * 60)
        print("🎉 PROCESSING COMPLETE!")
        print(f"✅ Successful: {successful}")
        print(f"❌ Failed: {failed}")
        print(f"📊 Total: {total_searches}")
        print(f"📁 Screenshots saved in: {self.screenshots_folder}")
        print(f"📋 Excel file updated: {self.excel_file}")
        print("=" * 60)

async def main():
    """Main function to run the scraper"""
    
    # First, create the Excel file with dummy data
    print("📝 Creating Excel file with dummy search queries...")
    from create_search_excel import create_search_excel
    excel_filename, _ = create_search_excel()
    
    print(f"\n🔧 Setting up scraper...")
    
    # Initialize scraper
    scraper = ExcelGoogleScraper(
        excel_file=excel_filename,
        screenshots_folder="google_screenshots"
    )
    
    # Process all searches
    await scraper.process_all_searches(delay_between_searches=3)

if __name__ == "__main__":
    print("🚀 Google Search Screenshot Automation")
    print("=" * 50)
    asyncio.run(main())
