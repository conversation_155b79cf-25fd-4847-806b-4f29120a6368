"""
Screenshot Analyzer - Check for bot detection and success rates
"""

import os
import pandas as pd
from PIL import Image
import pytesseract
import re

class ScreenshotAnalyzer:
    def __init__(self, screenshots_folder, excel_file):
        self.screenshots_folder = screenshots_folder
        self.excel_file = excel_file
        
        # Bot detection keywords
        self.bot_detection_keywords = [
            'unusual traffic',
            'captcha',
            'not a robot',
            'verify you are human',
            'suspicious activity',
            'automated queries',
            'recaptcha',
            'prove you are human',
            'security check',
            'verify that you are not a robot'
        ]
        
        # Success indicators
        self.success_keywords = [
            'search results',
            'about',
            'results',
            'web',
            'images',
            'news',
            'shopping',
            'maps'
        ]
    
    def analyze_screenshot_content(self, image_path):
        """
        Analyze screenshot content using OCR
        Returns: (is_bot_detected, is_successful, confidence_score)
        """
        try:
            # Open and process image
            image = Image.open(image_path)
            
            # Extract text using OCR
            text = pytesseract.image_to_string(image).lower()
            
            # Check for bot detection
            bot_detected = any(keyword in text for keyword in self.bot_detection_keywords)
            
            # Check for success indicators
            success_indicators = sum(1 for keyword in self.success_keywords if keyword in text)
            is_successful = success_indicators >= 2 and not bot_detected
            
            # Calculate confidence score
            confidence_score = min(100, success_indicators * 20) if is_successful else 0
            
            return bot_detected, is_successful, confidence_score, text[:200]
            
        except Exception as e:
            print(f"Error analyzing {image_path}: {e}")
            return False, False, 0, ""
    
    def analyze_all_screenshots(self):
        """
        Analyze all screenshots in the folder
        """
        if not os.path.exists(self.screenshots_folder):
            print(f"❌ Screenshots folder not found: {self.screenshots_folder}")
            return
        
        # Get all PNG files
        screenshot_files = [f for f in os.listdir(self.screenshots_folder) if f.endswith('.png')]
        
        if not screenshot_files:
            print(f"❌ No screenshots found in {self.screenshots_folder}")
            return
        
        print(f"🔍 Analyzing {len(screenshot_files)} screenshots...")
        
        results = []
        bot_detected_count = 0
        successful_count = 0
        
        for filename in screenshot_files:
            file_path = os.path.join(self.screenshots_folder, filename)
            
            print(f"📸 Analyzing: {filename}")
            
            bot_detected, is_successful, confidence, text_sample = self.analyze_screenshot_content(file_path)
            
            result = {
                'filename': filename,
                'bot_detected': bot_detected,
                'successful': is_successful,
                'confidence_score': confidence,
                'text_sample': text_sample
            }
            
            results.append(result)
            
            if bot_detected:
                bot_detected_count += 1
                print(f"  🚨 Bot detection found!")
            elif is_successful:
                successful_count += 1
                print(f"  ✅ Successful (confidence: {confidence}%)")
            else:
                print(f"  ❓ Unclear result")
        
        # Calculate statistics
        total_files = len(screenshot_files)
        success_rate = (successful_count / total_files) * 100
        bot_detection_rate = (bot_detected_count / total_files) * 100
        
        print("\n" + "=" * 60)
        print("📊 SCREENSHOT ANALYSIS RESULTS")
        print("=" * 60)
        print(f"📁 Total screenshots analyzed: {total_files}")
        print(f"✅ Successful screenshots: {successful_count}")
        print(f"🚨 Bot detection screenshots: {bot_detected_count}")
        print(f"❓ Unclear results: {total_files - successful_count - bot_detected_count}")
        print(f"📈 Success rate: {success_rate:.1f}%")
        print(f"🚨 Bot detection rate: {bot_detection_rate:.1f}%")
        
        # Recommendations
        if success_rate >= 90:
            print("\n🎉 EXCELLENT! Success rate above 90%")
        elif success_rate >= 70:
            print("\n👍 GOOD! Success rate above 70%")
        elif success_rate >= 50:
            print("\n⚠️ MODERATE! Success rate above 50% - consider improvements")
        else:
            print("\n❌ POOR! Success rate below 50% - major improvements needed")
        
        if bot_detection_rate > 20:
            print("\n🚨 HIGH BOT DETECTION RATE - Recommendations:")
            print("   - Increase delays between searches")
            print("   - Use different user agents")
            print("   - Try different Chrome profiles")
            print("   - Reduce search frequency")
        
        print("=" * 60)
        
        return results, success_rate, bot_detection_rate
    
    def update_excel_with_analysis(self, analysis_results):
        """
        Update Excel file with analysis results
        """
        try:
            # Load Excel file
            df = pd.read_excel(self.excel_file, sheet_name='Search Queries')
            
            # Create analysis lookup
            analysis_lookup = {result['filename']: result for result in analysis_results}
            
            # Update DataFrame
            for index, row in df.iterrows():
                filename = row.get('screenshot_filename', '')
                if filename in analysis_lookup:
                    analysis = analysis_lookup[filename]
                    
                    # Update status based on analysis
                    if analysis['bot_detected']:
                        df.at[index, 'status'] = 'Bot Detected (Confirmed)'
                        df.at[index, 'notes'] = 'Bot detection confirmed by OCR analysis'
                    elif analysis['successful']:
                        df.at[index, 'status'] = 'Successful (Confirmed)'
                        df.at[index, 'notes'] = f'Success confirmed by OCR (confidence: {analysis["confidence_score"]}%)'
                    else:
                        df.at[index, 'status'] = 'Unclear Result'
                        df.at[index, 'notes'] = 'Could not determine success from screenshot'
            
            # Save updated Excel
            with pd.ExcelWriter(self.excel_file, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='Search Queries', index=False)
                
                # Auto-adjust column widths
                workbook = writer.book
                worksheet = writer.sheets['Search Queries']
                
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            print(f"💾 Excel file updated with analysis results: {self.excel_file}")
            
        except Exception as e:
            print(f"❌ Error updating Excel file: {e}")

def main():
    """Main function to analyze screenshots"""
    import sys
    
    if len(sys.argv) < 3:
        print("Usage: python screenshot_analyzer.py <screenshots_folder> <excel_file>")
        return
    
    screenshots_folder = sys.argv[1]
    excel_file = sys.argv[2]
    
    analyzer = ScreenshotAnalyzer(screenshots_folder, excel_file)
    results, success_rate, bot_rate = analyzer.analyze_all_screenshots()
    
    if results:
        analyzer.update_excel_with_analysis(results)

if __name__ == "__main__":
    main()
