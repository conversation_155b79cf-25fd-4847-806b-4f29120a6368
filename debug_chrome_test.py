"""
Debug Chrome opening issue
"""

import asyncio
import os
from playwright.async_api import async_playwright

async def test_chrome_opening():
    """Test Chrome opening with different configurations"""
    
    # Get Chrome profile path
    username = os.getenv('USERNAME')
    chrome_profile_path = f"C:/Users/<USER>/AppData/Local/Google/Chrome/User Data"
    
    print(f"Testing Chrome profile path: {chrome_profile_path}")
    print(f"Profile exists: {os.path.exists(chrome_profile_path)}")
    
    async with async_playwright() as p:
        try:
            print("\n1. Testing basic Chromium launch...")
            browser = await p.chromium.launch(headless=False)
            page = await browser.new_page()
            await page.goto('https://www.google.com')
            print("✅ Basic Chromium launch successful")
            await browser.close()
            
        except Exception as e:
            print(f"❌ Basic Chromium launch failed: {e}")
            return
        
        try:
            print("\n2. Testing Chrome with profile (persistent context)...")
            browser = await p.chromium.launch_persistent_context(
                user_data_dir=chrome_profile_path,
                headless=False,
                args=[
                    '--no-first-run',
                    '--no-default-browser-check'
                ]
            )
            
            if len(browser.pages) > 0:
                page = browser.pages[0]
            else:
                page = await browser.new_page()
            
            await page.goto('https://www.google.com')
            print("✅ Chrome with profile launch successful")
            
            # Wait a bit to see the browser
            await asyncio.sleep(3)
            
            await browser.close()
            
        except Exception as e:
            print(f"❌ Chrome with profile launch failed: {e}")
            print("This might be because Chrome is already running or profile is locked")
            
            # Try alternative approach
            try:
                print("\n3. Testing alternative Chrome launch...")
                browser = await p.chromium.launch(
                    headless=False,
                    args=[
                        '--no-first-run',
                        '--no-default-browser-check',
                        '--disable-blink-features=AutomationControlled'
                    ]
                )
                page = await browser.new_page()
                await page.goto('https://www.google.com')
                print("✅ Alternative Chrome launch successful")
                
                await asyncio.sleep(3)
                await browser.close()
                
            except Exception as e2:
                print(f"❌ Alternative Chrome launch also failed: {e2}")

if __name__ == "__main__":
    print("🔧 Chrome Opening Debug Test")
    print("=" * 40)
    asyncio.run(test_chrome_opening())
